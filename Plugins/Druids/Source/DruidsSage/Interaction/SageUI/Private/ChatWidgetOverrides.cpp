#include "ChatWidgetOverrides.h"
#include "DruidsSageChatShell.h"
#include "ChatItems/DruidsSageSimpleChatItem.h"
#include "ChatItems/DruidsSageActionRequestChatItem.h"
#include "ChatItems/DruidsSageAssistantChatItem.h"
#include "ChatItems/DruidsSageAssistantTextChatItem.h"
#include "ChatItems/DruidsSageThinkingContainerChatItem.h"
#include "ChatItems/DruidsSageThinkingChatItem.h"
#include "ChatItems/DruidsSageThinkingDetailChatItem.h"

UChatWidgetOverrides::UChatWidgetOverrides()
{
	// Initialize with nullptr - will use base classes by default
	DefaultChatShellClass = nullptr;
	DefaultSimpleChatItemClass = nullptr;
	DefaultActionRequestChatItemClass = nullptr;
	DefaultSuggestedPromptsChatItemClass = nullptr;
	DefaultAssistantChatItemClass = nullptr;
	DefaultAssistantTextChatItemClass = nullptr;
	DefaultThinkingContainerChatItemClass = nullptr;
	DefaultThinkingChatItemClass = nullptr;
	DefaultThinkingDetailChatItemClass = nullptr;
}

TSubclassOf<UDruidsSageChatShell> UChatWidgetOverrides::GetChatShellWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultChatShellClass;
}

TSubclassOf<UDruidsSageSimpleChatItem> UChatWidgetOverrides::GetSimpleChatItemWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultSimpleChatItemClass;
}

TSubclassOf<UDruidsSageActionRequestChatItem> UChatWidgetOverrides::GetActionRequestChatItemWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultActionRequestChatItemClass;
}

TSubclassOf<UDruidsSageSuggestedPromptsChatItem> UChatWidgetOverrides::GetSuggestedPromptsChatItemWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultSuggestedPromptsChatItemClass;
}

TSubclassOf<UDruidsSageAssistantChatItem> UChatWidgetOverrides::GetAssistantChatItemWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultAssistantChatItemClass;
}

TSubclassOf<UDruidsSageAssistantTextChatItem> UChatWidgetOverrides::GetAssistantTextChatItemWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultAssistantTextChatItemClass;
}

TSubclassOf<UDruidsSageThinkingContainerChatItem> UChatWidgetOverrides::GetThinkingContainerChatItemWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultThinkingContainerChatItemClass;
}

TSubclassOf<UDruidsSageThinkingChatItem> UChatWidgetOverrides::GetThinkingChatItemWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultThinkingChatItemClass;
}

TSubclassOf<UDruidsSageThinkingDetailChatItem> UChatWidgetOverrides::GetThinkingDetailChatItemWidgetClass_Implementation() const
{
	// Return the default class if set, otherwise nullptr (which means use base class)
	return DefaultThinkingDetailChatItemClass;
}

#pragma once

#include <CoreMinimal.h>

#include "DruidsSageChatTypes.h"
#include "IDruidsSageChatItem.h"

#include "DruidsSageSuggestedPromptsChatItem.generated.h"

class UDruidsSageMessagingHandler;
class UTextBlock;
class UButton;
class ISuggestedPromptsHandler;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnSuggestedPromptSelectedUMG, const FString&, PromptText, UDruidsSageSuggestedPromptsChatItem*, ChatItem);

UCLASS(BlueprintType, Blueprintable)
class SAGEUI_API UDruidsSageSuggestedPromptsChatItem : public UIDruidsSageChatItem
{
	GENERATED_BODY()

public:
	UDruidsSageSuggestedPromptsChatItem(const FObjectInitializer& ObjectInitializer);

	// UUserWidget interface
	virtual void NativePreConstruct() override;
	virtual void NativeConstruct() override;
	virtual void SynchronizeProperties() override;
	// End of UUserWidget interface

	// UIDruidsSageChatItem interface implementation
	virtual FName GetTypeName() const override;
	virtual void FillInDruidsMessage(FDruidsSageChatMessage& Message) const override;
	virtual EDruidsSageChatRole GetMessageRole() const override;
	virtual TWeakObjectPtr<UDruidsSageMessagingHandler> GetMessagingHandler() const override;
	virtual void UpdateFromContentJson(const TSharedPtr<FJsonValue>& ContentJson) override;
	virtual FString GetPlainText() const override;
	virtual void OnPromptMessageSent() override;

	UFUNCTION(BlueprintCallable, Category = "Chat Item")
	void SetScrollBoxReference(class UScrollBox* InScrollBox);

	/**
	 * Set the suggested prompts handler for processing fresh conversation prompts
	 * @param Handler The handler to use for processing suggested prompts
	 */
	void SetSuggestedPromptsHandler(TSharedPtr<ISuggestedPromptsHandler> Handler);

	/**
	 * Populate the chat item with saved suggested prompts from the handler
	 * This can be used to show previously saved prompts
	 */
	void PopulateFromSavedPrompts();

	// Getters for buttons
	UButton* GetPromptButton1() const { return PromptButton1; }
	UButton* GetPromptButton2() const { return PromptButton2; }

	// Events
	UPROPERTY(BlueprintAssignable, Category = "Chat Item")
	FOnSuggestedPromptSelectedUMG OnSuggestedPromptSelected;

	static FName GetClassName() { return "UDruidsSageSuggestedPromptsChatItem"; }

protected:
	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	TObjectPtr<UButton> PromptButton1;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	TObjectPtr<UButton> PromptButton2;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	TObjectPtr<UTextBlock> PromptButton1Text;

	UPROPERTY(meta = (BindWidget), BlueprintReadOnly, Category = "Chat Item")
	TObjectPtr<UTextBlock> PromptButton2Text;

private:
	TWeakObjectPtr<UDruidsSageMessagingHandler> MessagingHandler;
	TSharedPtr<FJsonValue> ContentJsonValue;
	TSharedPtr<ISuggestedPromptsHandler> SuggestedPromptsHandler;

	// Store the prompt texts and summaries for the buttons
	FString PromptText1;
	FString PromptText2;
	FString SummaryText1;
	FString SummaryText2;

	UFUNCTION()
	void OnPromptButton1Clicked();

	UFUNCTION()
	void OnPromptButton2Clicked();

	void SetupWidgets();
};

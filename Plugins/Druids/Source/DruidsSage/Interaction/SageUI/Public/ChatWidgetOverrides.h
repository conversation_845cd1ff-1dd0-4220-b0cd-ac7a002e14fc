#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "DruidsSageChatShell.h"
#include "ChatWidgetOverrides.generated.h"

// Forward declarations for chat item classes
class UDruidsSageSimpleChatItem;
class UDruidsSageActionRequestChatItem;
class UDruidsSageSuggestedPromptsChatItem;
class UDruidsSageAssistantChatItem;
class UDruidsSageThinkingContainerChatItem;
class UDruidsSageThinkingChatItem;
class UDruidsSageThinkingDetailChatItem;

/**
 * ChatWidgetOverrides class that can be overridden in Blueprint to specify
 * which UDruidsSageChatShell class to instantiate instead of the base class.
 * This allows for Blueprint customization of the chat shell widget.
 */
UCLASS(Blueprintable, BlueprintType)
class SAGEUI_API UChatWidgetOverrides : public UObject
{
	GENERATED_BODY()

public:
	UChatWidgetOverrides();

	/**
	 * Gets the widget class to instantiate for the chat shell.
	 * Can be overridden in Blueprint to return a custom UDruidsSageChatShell subclass.
	 * @return The UDruidsSageChatShell class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageChatShell> GetChatShellWidgetClass() const;

	/**
	 * Gets the widget class to instantiate for simple chat items.
	 * Can be overridden in Blueprint to return a custom UDruidsSageSimpleChatItem subclass.
	 * @return The UDruidsSageSimpleChatItem class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageSimpleChatItem> GetSimpleChatItemWidgetClass() const;

	/**
	 * Gets the widget class to instantiate for action request chat items.
	 * Can be overridden in Blueprint to return a custom UDruidsSageActionRequestChatItem subclass.
	 * @return The UDruidsSageActionRequestChatItem class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageActionRequestChatItem> GetActionRequestChatItemWidgetClass() const;

	/**
	 * Gets the widget class to instantiate for suggested prompts chat items.
	 * Can be overridden in Blueprint to return a custom UDruidsSageSuggestedPromptsChatItem subclass.
	 * @return The UDruidsSageSuggestedPromptsChatItem class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageSuggestedPromptsChatItem> GetSuggestedPromptsChatItemWidgetClass() const;

	/**
	 * Gets the widget class to instantiate for assistant chat items.
	 * Can be overridden in Blueprint to return a custom UDruidsSageAssistantChatItem subclass.
	 * @return The UDruidsSageAssistantChatItem class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageAssistantChatItem> GetAssistantChatItemWidgetClass() const;

	/**
	 * Gets the widget class to instantiate for an assistant text chat item
	 * Can be overridden in Blueprint to return a custom UDruidsSageAssistantTextChatItem subclass.
	 * @return The UDruidsSageAssistantTextChatItem class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrrides")
	TSubclassOf<UDruidsSageAssistantTextChatItem> GetAssistantTextChatItemWidgetClass() const;

	/**
	 * Gets the widget class to instantiate for thinking container chat items.
	 * Can be overridden in Blueprint to return a custom UDruidsSageThinkingContainerChatItem subclass.
	 * @return The UDruidsSageThinkingContainerChatItem class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageThinkingContainerChatItem> GetThinkingContainerChatItemWidgetClass() const;

	/**
	 * Gets the widget class to instantiate for thinking chat items.
	 * Can be overridden in Blueprint to return a custom UDruidsSageThinkingChatItem subclass.
	 * @return The UDruidsSageThinkingChatItem class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageThinkingChatItem> GetThinkingChatItemWidgetClass() const;

	/**
	 * Gets the widget class to instantiate for thinking detail chat items.
	 * Can be overridden in Blueprint to return a custom UDruidsSageThinkingDetailChatItem subclass.
	 * @return The UDruidsSageThinkingDetailChatItem class to instantiate, or nullptr to use the base class
	 */
	UFUNCTION(BlueprintNativeEvent, BlueprintCallable, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageThinkingDetailChatItem> GetThinkingDetailChatItemWidgetClass() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetChatShellWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageChatShell> GetChatShellWidgetClass_Implementation() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetSimpleChatItemWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageSimpleChatItem> GetSimpleChatItemWidgetClass_Implementation() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetActionRequestChatItemWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageActionRequestChatItem> GetActionRequestChatItemWidgetClass_Implementation() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetSuggestedPromptsChatItemWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageSuggestedPromptsChatItem> GetSuggestedPromptsChatItemWidgetClass_Implementation() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetAssistantChatItemWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageAssistantChatItem> GetAssistantChatItemWidgetClass_Implementation() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetAssistantTextChatItemWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageAssistantTextChatItem> GetAssistantTextChatItemWidgetClass_Implementation() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetThinkingContainerChatItemWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageThinkingContainerChatItem> GetThinkingContainerChatItemWidgetClass_Implementation() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetThinkingChatItemWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageThinkingChatItem> GetThinkingChatItemWidgetClass_Implementation() const;

	/**
	 * C++ implementation that returns the base class by default.
	 * Blueprint implementations should override GetThinkingDetailChatItemWidgetClass instead.
	 */
	virtual TSubclassOf<UDruidsSageThinkingDetailChatItem> GetThinkingDetailChatItemWidgetClass_Implementation() const;

protected:
	/**
	 * The default chat shell widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageChatShell.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageChatShell> DefaultChatShellClass;

	/**
	 * The default simple chat item widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageSimpleChatItem.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageSimpleChatItem> DefaultSimpleChatItemClass;

	/**
	 * The default action request chat item widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageActionRequestChatItem.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageActionRequestChatItem> DefaultActionRequestChatItemClass;

	/**
	 * The default suggested prompts chat item widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageSuggestedPromptsChatItem.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageSuggestedPromptsChatItem> DefaultSuggestedPromptsChatItemClass;

	/**
	 * The default assistant chat item widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageAssistantChatItem.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageAssistantChatItem> DefaultAssistantChatItemClass;

	/**
	 * The default assistant chat item widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageAssistantTextChatItem.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageAssistantTextChatItem> DefaultAssistantTextChatItemClass;

	/**
	 * The default thinking container chat item widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageThinkingContainerChatItem.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageThinkingContainerChatItem> DefaultThinkingContainerChatItemClass;

	/**
	 * The default thinking chat item widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageThinkingChatItem.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageThinkingChatItem> DefaultThinkingChatItemClass;

	/**
	 * The default thinking detail chat item widget class to use if no override is specified.
	 * This can be set in Blueprint or left as nullptr to use UDruidsSageThinkingDetailChatItem.
	 */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Chat Widget Overrides")
	TSubclassOf<UDruidsSageThinkingDetailChatItem> DefaultThinkingDetailChatItemClass;
};
